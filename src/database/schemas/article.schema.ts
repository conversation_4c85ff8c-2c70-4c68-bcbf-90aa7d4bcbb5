import { RxJsonSchema } from 'rxdb';
import { ArticleDocument } from '../../types/Article';

export const articleSchema: RxJsonSchema<ArticleDocument> = {
  title: 'article schema',
  version: 0,
  description: 'describes an article',
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 100,
    },
    _id: {
      type: 'string',
      maxLength: 100,
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    qty: {
      type: 'number',
      minimum: 0,
    },
    selling_price: {
      type: 'number',
      minimum: 0,
    },
    business_id: {
      type: 'string',
      maxLength: 100,
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    _deleted: {
      type: 'boolean',
    },
  },
  required: [
    'id',
    '_id',
    'name',
    'qty',
    'selling_price',
    'business_id',
    'createdAt',
    'updatedAt',
  ],
  indexes: ['business_id', 'name', 'createdAt'],
};
