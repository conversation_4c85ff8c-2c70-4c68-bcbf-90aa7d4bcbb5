import { RxJsonSchema } from 'rxdb';
import { BusinessDocument } from '../../types/Business';

export const businessSchema: RxJsonSchema<BusinessDocument> = {
  title: 'business schema',
  version: 0,
  description: 'describes a business',
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 100,
    },
    _id: {
      type: 'string',
      maxLength: 100,
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    _deleted: {
      type: 'boolean',
    },
    _rev: undefined
  },
  required: ['id', '_id', 'name', 'createdAt', 'updatedAt'],
  indexes: ['name', 'createdAt'],
};
